import { Routes, Route } from 'react-router-dom';
// import Home from '../pages/Home';
import CreateCourse from '../pages/CreateCourse';
// import EditCourse from '../pages/EditCourse';
// import CourseDetail from '../pages/CourseDetail';

export default function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={"Home"} />
      <Route path="/create" element={<CreateCourse />} />
      {/* <Route path="/edit/:courseId" element={<EditCourse />} /> */}
      {/* <Route path="/course/:courseId" element={<CourseDetail />} /> */}
    </Routes>
  );
}
