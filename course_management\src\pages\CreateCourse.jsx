import { useDispatch } from 'react-redux';
import { addCourse } from '../features/courses/courseSlice';
import CourseForm from '../features/courses/CourseForm';
import { useNavigate } from 'react-router-dom';

export default function CreateCourse() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const handleSubmit = (courseData) => {
    dispatch(addCourse(courseData));
    navigate("/"); // redirect to home or list page
  };

  return (
    <div className="p-6">
      <CourseForm onSubmit={handleSubmit} />
    </div>
  );
}
