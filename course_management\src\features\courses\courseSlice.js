import { createSlice } from '@reduxjs/toolkit';

const loadCoursesFromStorage = () => {
  const stored = localStorage.getItem("courses");
  return stored ? JSON.parse(stored) : [];
};

const saveCoursesToStorage = (courses) => {
  localStorage.setItem("courses", JSON.stringify(courses));
};

const courseSlice = createSlice({
  name: 'courses',
  initialState: {
    items: loadCoursesFromStorage(),
  },
  reducers: {
    addCourse: (state, action) => {
      const newCourse = { ...action.payload, id: Date.now().toString() };
      state.items.push(newCourse);
      saveCoursesToStorage(state.items);
    },
  },
});

export const { addCourse } = courseSlice.actions;
export default courseSlice.reducer;
