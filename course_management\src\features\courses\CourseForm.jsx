import { useState } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";

const defaultCourse = {
  title: "",
  description: "",
  thumbnail: "",
  category: "Programming",
  difficulty: "Beginner",
};

const categories = ["Programming", "Design", "Business"];
const difficultyLevels = ["Beginner", "Intermediate", "Advanced"];

export default function CourseForm({ onSubmit }) {
  const [courseData, setCourseData] = useState(defaultCourse);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCourseData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDescriptionChange = (value) => {
    setCourseData((prev) => ({ ...prev, description: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (courseData.title.length < 10 || courseData.title.length > 60) {
      alert("Title must be between 10 and 60 characters.");
      return;
    }
    onSubmit(courseData);
    setCourseData(defaultCourse); // reset
  };

  return (
    <form className="max-w-2xl mx-auto p-4 bg-white shadow rounded" onSubmit={handleSubmit}>
      <h2 className="text-xl font-semibold mb-4">Create New Course</h2>

      <label className="block mb-2">Title</label>
      <input
        type="text"
        name="title"
        value={courseData.title}
        onChange={handleChange}
        required
        className="w-full border p-2 mb-4"
      />

      <label className="block mb-2">Description</label>
      <ReactQuill
        value={courseData.description}
        onChange={handleDescriptionChange}
        className="mb-4"
      />

      <label className="block mb-2">Thumbnail URL</label>
      <input
        type="text"
        name="thumbnail"
        value={courseData.thumbnail}
        onChange={handleChange}
        className="w-full border p-2 mb-4"
      />

      <label className="block mb-2">Category</label>
      <select
        name="category"
        value={courseData.category}
        onChange={handleChange}
        className="w-full border p-2 mb-4"
      >
        {categories.map((cat) => (
          <option key={cat}>{cat}</option>
        ))}
      </select>

      <label className="block mb-2">Difficulty</label>
      <select
        name="difficulty"
        value={courseData.difficulty}
        onChange={handleChange}
        className="w-full border p-2 mb-4"
      >
        {difficultyLevels.map((level) => (
          <option key={level}>{level}</option>
        ))}
      </select>

      <button
        type="submit"
        className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
      >
        Save Course
      </button>
    </form>
  );
}
